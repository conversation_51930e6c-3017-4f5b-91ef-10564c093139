<?php

namespace App\Form;

use App\Entity\User;
use App\Entity\Country;
use App\Service\EmailUniquenessValidator;
use App\Repository\CountryRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;

use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\LessThan;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class RegistrationFormType extends AbstractType
{
    public function __construct(
        private EmailUniquenessValidator $emailValidator,
        private CountryRepository $countryRepository
    ) {}

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('firstName', TextType::class, [
                'label' => 'First Name',
                'attr' => [
                    'class' => 'floating-input',
                    'placeholder' => ' ',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter your first name']),
                ],
            ])
            ->add('lastName', TextType::class, [
                'label' => 'Last Name',
                'attr' => [
                    'class' => 'floating-input',
                    'placeholder' => ' ',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter your last name']),
                ],
            ])
            ->add('dateOfBirth', DateType::class, [
                'label' => 'Date of Birth',
                'required' => false,
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'html5' => true,
                'attr' => [
                    'class' => 'floating-input',
                    'placeholder' => ' ',
                    'type' => 'date',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                ],
                'constraints' => [
                    new LessThan([
                        'value' => 'today',
                        'message' => 'Date of birth must be in the past'
                    ])
                ]
            ])
            ->add('email', EmailType::class, [
                'label' => 'Email Address',
                'attr' => [
                    'class' => 'floating-input',
                    'placeholder' => ' ',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter an email address']),
                    new Email(['message' => 'Please enter a valid email address']),
                    new Callback([$this, 'validateEmailUniqueness']),
                ],
            ])
            ->add('phone', TextType::class, [
                'label' => 'Phone Number',
                'required' => false,
                'attr' => [
                    'class' => 'floating-input',
                    'placeholder' => ' ',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                ],
            ])
            ->add('country', EntityType::class, [
                'class' => Country::class,
                'choice_label' => 'countryName',
                'label' => 'Country',
                'attr' => [
                    'class' => 'floating-input enhanced-dropdown',
                    'placeholder' => ' ',
                    'style' => 'height: 60px; font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;',
                    'id' => 'country-select'
                ],
                'required' => false,
                'placeholder' => 'Choose a country...',
                'query_builder' => function ($repository) {
                    return $repository->createQueryBuilder('c')
                        ->orderBy('c.countryName', 'ASC');
                }
            ])
            ->add('profilePicture', FileType::class, [
                'label' => 'Profile Picture',
                'required' => false,
                'mapped' => false,
                'attr' => [
                    'class' => 'floating-input profile-picture-input',
                    'accept' => 'image/*',
                    'id' => 'profile-picture-input',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px) !important; min-height: calc(1.6em + 1.25rem + 4px) !important; border: none; border-bottom: 2px solid #e8ecef; background: transparent; padding: 22px 0 12px 0; font-size: 1.05rem; color: var(--ca-dark-gray); outline: none; font-weight: 500; display: flex; align-items: center;'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => ['image/jpeg', 'image/png', 'image/gif'],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, or GIF)',
                    ])
                ],
            ])
            ->add('plainPassword', RepeatedType::class, [
                'type' => PasswordType::class,
                'mapped' => false,
                'first_options' => [
                    'label' => 'Password',
                    'attr' => [
                        'class' => 'floating-input',
                        'placeholder' => ' ',
                        'autocomplete' => 'new-password',
                        'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                    ],
                ],
                'second_options' => [
                    'label' => 'Confirm Password',
                    'attr' => [
                        'class' => 'floating-input',
                        'placeholder' => ' ',
                        'autocomplete' => 'new-password',
                        'style' => 'height: calc(1.6em + 1.25rem + 4px);'
                    ],
                ],
                'invalid_message' => 'The password fields must match.',
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a password']),
                    new Length([
                        'min' => 6,
                        'minMessage' => 'Your password should be at least {{ limit }} characters',
                        'max' => 4096,
                    ]),
                ],
            ]);
    }

    public function validateEmailUniqueness($email, ExecutionContextInterface $context): void
    {
        if (!$this->emailValidator->isEmailUnique($email)) {
            $context->buildViolation($this->emailValidator->getEmailDuplicateMessage($email))
                ->addViolation();
        }
    }



    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => User::class,
        ]);
    }
}
