<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capitol Academy - Register</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .register-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1200px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 700px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .branding-content {
            position: relative;
            z-index: 2;
            width: 100%;
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 300px;
            height: 300px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .brand-logo-round:hover {
            transform: scale(1.05) rotate(2deg);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin: 0 0 15px 0;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .why-choose-section {
            padding: 20px 0;
            animation: slideInRight 0.8s ease-out 0.3s both;
        }

        .why-choose-section h3 {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: white;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 20px 18px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .feature-card-content {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .feature-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #dc3545 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        .feature-icon i {
            font-size: 1.2rem;
            color: white;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(169, 4, 24, 0.4);
        }

        .feature-text {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Card animations */
        .feature-card:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.4s both; }
        .feature-card:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.5s both; }
        .feature-card:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.6s both; }
        .feature-card:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.7s both; }

        .trust-indicators {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            margin-top: 25px;
        }

        .trust-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 15px 18px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(8px);
            position: relative;
            overflow: hidden;
        }

        .trust-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.08), transparent);
            transition: left 0.5s ease;
        }

        .trust-card:hover::before {
            left: 100%;
        }

        .trust-card:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .trust-card-content {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
        }

        .trust-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, #1e3c72 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(1, 26, 45, 0.3);
        }

        .trust-icon i {
            font-size: 1rem;
            color: white;
        }

        .trust-card:hover .trust-icon {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.4);
        }

        .trust-text {
            font-size: 0.95rem;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Right Side - Form */
        .register-form-section {
            padding: 40px 35px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
            overflow-y: auto;
        }

        .form-container {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
            animation: fadeInUp 0.6s ease;
        }

        .form-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--ca-dark-gray);
            margin: 0 0 10px 0;
            letter-spacing: -0.3px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 0.95rem;
            margin: 0;
            font-weight: 400;
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 24px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            border: none;
            border-bottom: 2px solid #e8ecef;
            background: transparent;
            font-size: 1.05rem;
            color: var(--ca-dark-gray);
            outline: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            height: calc(1.6em + 1.25rem + 4px);
            line-height: 1.5;
            display: flex;
            align-items: center;
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
            background: transparent !important;
            transform: translateY(-1px);
        }

        .floating-input:hover:not(:focus) {
            border-bottom-color: var(--ca-focus-blue-border);
        }

        .floating-input:focus::placeholder {
            opacity: 0;
        }

        .floating-label {
            position: absolute;
            top: 22px;
            left: 0;
            font-size: 1.05rem;
            color: var(--ca-medium-gray);
            pointer-events: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            transform-origin: left top;
        }

        .floating-input:focus + .floating-label,
        .floating-input:valid + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: -12px;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 700;
            transform: scale(0.9);
            text-shadow: 0 1px 2px rgba(1, 26, 45, 0.1);
        }

        .floating-input:hover:not(:focus) + .floating-label {
            color: var(--ca-focus-blue-border);
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--ca-focus-blue) 0%, var(--ca-secondary-blue) 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.3);
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        .floating-input:hover:not(:focus) ~ .input-border {
            width: 30%;
            background: linear-gradient(135deg, var(--ca-focus-blue-border) 0%, rgba(26, 52, 97, 0.5) 100%);
        }

        /* Enhanced Country Dropdown Styling - Matching Admin Course Creation Design */
        .enhanced-dropdown {
            transition: all 0.3s ease !important;
            font-size: 1rem !important;
            border: 2px solid #ced4da !important;
            background-color: #fff !important;
            cursor: pointer;
            font-weight: 500;
            height: 50px !important;
            min-height: 50px !important;
            border-radius: 8px !important;
            padding: 0.75rem 1rem !important;
            line-height: 1.5 !important;
            display: flex !important;
            align-items: center !important;
        }

        .enhanced-dropdown:hover {
            border-color: #011a2d !important;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15) !important;
            background-color: #f8f9fa !important;
        }

        .enhanced-dropdown:focus {
            border-color: #011a2d !important;
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
            outline: none;
            background-color: #fff !important;
            transform: translateY(-1px);
        }

        /* Country dropdown label styling to match admin forms */
        .country-label {
            color: #343a40;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .country-label i {
            color: #007bff;
            margin-right: 0.5rem;
        }

        /* Select2 Bootstrap4 Theme Styling - Matching Admin Course Form */
        .select2-container--bootstrap4 .select2-selection--single {
            height: 50px !important;
            min-height: 50px !important;
            border: 2px solid #ced4da !important;
            border-radius: 8px !important;
            font-size: 1rem !important;
            box-sizing: border-box !important;
            background-color: #fff !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
            line-height: 1.5 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            color: #343a40 !important;
            display: flex !important;
            align-items: center !important;
            height: 100% !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
            height: 50px !important;
            right: 1rem !important;
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow b {
            border-color: #343a40 transparent transparent transparent !important;
            border-style: solid !important;
            border-width: 5px 4px 0 4px !important;
            height: 0 !important;
            left: 50% !important;
            margin-left: -4px !important;
            margin-top: -2px !important;
            position: absolute !important;
            top: 50% !important;
            width: 0 !important;
            font-weight: 500 !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
            height: calc(1.6em + 1.25rem) !important;
            top: 2px !important;
            right: 2px !important;
        }

        .select2-container--bootstrap4 .select2-selection--single:hover {
            border-color: #011a2d !important;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15) !important;
            background-color: #f8f9fa !important;
        }

        .select2-container--bootstrap4.select2-container--focus .select2-selection--single {
            border-color: #011a2d !important;
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
            transform: translateY(-1px);
        }

        .select2-container--bootstrap4 .select2-dropdown {
            border: 2px solid #011a2d !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.15) !important;
        }

        .select2-container--bootstrap4 .select2-results__option--highlighted {
            background-color: #011a2d !important;
            color: white !important;
        }

        .select2-container--bootstrap4 .select2-results__option:hover {
            background-color: #a90418 !important;
            color: white !important;
        }

        /* Ensure Select2 containers match form field heights exactly */
        .select2-container {
            height: 50px !important;
        }

        .select2-container .select2-selection {
            height: 50px !important;
        }

        /* Enhanced dropdown option styling for better readability */
        .enhanced-dropdown option {
            padding: 0.75rem 1rem;
            background-color: #fff;
            color: var(--ca-primary-blue);
            font-weight: 500;
            line-height: 1.5;
            border-bottom: 1px solid #f1f3f4;
        }

        .enhanced-dropdown option:first-child {
            color: #6c757d;
            font-style: italic;
            font-weight: 400;
        }

        .enhanced-dropdown option:not(:first-child) {
            color: var(--ca-primary-blue);
            font-weight: 500;
        }

        .enhanced-dropdown option:hover {
            background-color: #f8f9fa;
            color: var(--ca-primary-blue);
        }

        .enhanced-dropdown option:checked,
        .enhanced-dropdown option:selected {
            background-color: var(--ca-primary-blue);
            color: white;
            font-weight: 600;
        }

        /* Profile Picture Upload Styling */
        .profile-picture-upload {
            position: relative;
            margin-bottom: 24px;
        }

        .profile-picture-preview {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #e8ecef;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .profile-picture-preview:hover {
            border-color: var(--ca-focus-blue);
            box-shadow: 0 6px 20px rgba(1, 26, 45, 0.2);
            transform: scale(1.02);
        }

        .profile-picture-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .profile-picture-preview .placeholder-icon {
            font-size: 2.5rem;
            color: var(--ca-medium-gray);
            transition: all 0.3s ease;
        }

        .profile-picture-preview:hover .placeholder-icon {
            color: var(--ca-focus-blue);
            transform: scale(1.1);
        }

        .profile-picture-preview .fa-spinner {
            color: var(--ca-focus-blue);
            font-size: 2rem;
        }

        .profile-upload-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e8ecef;
            border-radius: 8px;
            background: transparent;
            font-size: 0.95rem;
            color: var(--ca-dark-gray);
            transition: all 0.3s ease;
        }

        .profile-upload-input:hover {
            border-color: var(--ca-focus-blue-border);
        }

        .profile-upload-input:focus {
            border-color: var(--ca-focus-blue);
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25);
            outline: none;
        }

        /* Register Button */
        .register-btn {
            width: 100%;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            border: none;
            border-radius: 12px;
            color: var(--ca-white);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .register-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(169, 4, 24, 0.4);
        }

        .register-btn:focus {
            outline: 3px solid var(--ca-focus-blue-border);
            outline-offset: 3px;
        }

        .register-btn:active {
            transform: translateY(-1px);
        }

        .btn-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }

        .register-btn:hover .btn-icon {
            transform: translateX(8px);
        }

        /* Alerts */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            font-size: 0.9rem;
            animation: slideInDown 0.5s ease;
        }

        .alert-danger {
            background: rgba(169, 4, 24, 0.1);
            color: var(--ca-accent-red-dark);
            border: 1px solid rgba(169, 4, 24, 0.2);
            border-left: 4px solid var(--ca-accent-red);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid var(--ca-success-green);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        /* Simple text link for login */
        .login-text-link {
            color: var(--ca-medium-gray);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            margin-top: 15px;
            display: block;
        }

        .login-text-link:hover {
            color: var(--ca-focus-blue);
            text-decoration: underline;
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes slideShine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .register-wrapper {
                grid-template-columns: 1fr;
                max-width: 500px;
            }

            .login-branding {
                padding: 40px 30px;
                min-height: 300px;
            }

            .brand-title {
                font-size: 2.2rem;
            }

            .features-grid {
                gap: 12px;
                margin-bottom: 20px;
            }

            .feature-card {
                padding: 16px 15px;
            }

            .feature-icon {
                width: 40px;
                height: 40px;
                margin-right: 12px;
            }

            .feature-icon i {
                font-size: 1.1rem;
            }

            .feature-text {
                font-size: 0.95rem;
            }

            .trust-card {
                padding: 12px 15px;
            }

            .trust-icon {
                width: 30px;
                height: 30px;
                margin-right: 10px;
            }

            .trust-icon i {
                font-size: 0.9rem;
            }

            .trust-text {
                font-size: 0.9rem;
            }

            .register-form-section {
                padding: 30px 25px;
            }

            .form-title {
                font-size: 1.7rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .register-btn {
                padding: 16px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 15px;
            }

            .login-branding {
                padding: 30px 20px;
            }

            .register-form-section {
                padding: 30px 20px;
            }

            .brand-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .register-btn {
                padding: 16px 25px;
                font-size: 0.95rem;
            }

            .form-container {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-wrapper">
            <!-- Left Side - Branding -->
            <div class="login-branding">
                <div class="branding-content">
                    <div class="logo-section">
                        <img src="{{ asset('images/logos/logo-round.png') }}" alt="Capitol Academy" class="brand-logo-round">
                        <h1 class="brand-title">Capitol Academy</h1>
                    </div>
                </div>
            </div>



            <!-- Right Side - Registration Form -->
            <div class="register-form-section">
                <div class="form-container">
                    <!-- Form Header -->
                    <div class="form-header">
                        <h2 class="form-title">Join Capitol Academy</h2>
                        <p class="form-subtitle">Create your account to start learning</p>
                    </div>

                    <!-- Error Messages -->
                    {% for flash_error in app.flashes('verify_email_error') %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>{{ flash_error }}</span>
                        </div>
                    {% endfor %}

                    <!-- Registration Form -->
                    {{ form_start(registrationForm) }}
                        <!-- Name Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <!-- First Name -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.firstName, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-user" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        First Name
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Last Name -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.lastName, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-user" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Last Name
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Info Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Date of Birth -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.dateOfBirth, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-calendar" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Date of Birth
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Email -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.email, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-envelope" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Email Address
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Info Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Country -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.country, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-globe" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Country
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Phone -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.phone, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-phone" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Phone Number
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Picture (Full Width) -->
                        <div class="row">
                            <div class="col-12">
                                <div class="profile-picture-upload" style="margin-top: 32px; margin-bottom: 32px;">
                                    <label class="form-label">
                                        <i class="fas fa-camera" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Profile Picture
                                    </label>
                                    {{ form_widget(registrationForm.profilePicture, {
                                        'attr': {
                                            'class': 'profile-upload-input',
                                            'id': 'profilePictureInput'
                                        }
                                    }) }}
                                    <div class="profile-picture-preview" id="profilePreview">
                                        <i class="fas fa-user placeholder-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Password Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Password -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.plainPassword.first, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-lock" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Password
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Confirm Password -->
                                <div class="floating-label-group">
                                    {{ form_widget(registrationForm.plainPassword.second, {
                                        'attr': {
                                            'class': 'floating-input',
                                            'placeholder': ' '
                                        }
                                    }) }}
                                    <label class="floating-label">
                                        <i class="fas fa-lock" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                        Confirm Password
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Register Button -->
                        <button type="submit" class="register-btn">
                            <i class="fas fa-user-plus btn-icon"></i>
                            <span>Create Account</span>
                        </button>
                    {{ form_end(registrationForm) }}

                    <!-- Form Footer -->
                    <div class="form-footer">
                        <a href="{{ path('app_home') }}" class="back-link">
                            <i class="fas fa-arrow-left me-2"></i>Back to Website
                        </a>
                        <a href="{{ path('app_login') }}" class="login-text-link">
                            Already have an account? Sign in here
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Select2 for country dropdown
            const countrySelect = document.getElementById('country-select') || document.querySelector('select[name*="country"]');
            if (countrySelect) {
                $(countrySelect).select2({
                    placeholder: 'Choose a country...',
                    allowClear: true,
                    width: '100%',
                    theme: 'bootstrap4'
                });
            }

            // Country field is now a regular select with floating label

            // Handle floating labels for all inputs
            const inputs = document.querySelectorAll('.floating-input');

            inputs.forEach(input => {
                // Handle autofill detection
                const checkAutofill = () => {
                    if (input.value !== '') {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                };

                input.addEventListener('input', checkAutofill);
                input.addEventListener('blur', checkAutofill);

                // Initial check
                setTimeout(checkAutofill, 100);
            });



            // Profile Picture Preview - Enhanced with better error handling
            function initializeProfilePicturePreview() {
                // Try multiple ways to find the profile input
                let profileInput = document.getElementById('profilePictureInput');
                if (!profileInput) {
                    // Fallback: find by name attribute
                    profileInput = document.querySelector('input[name*="profilePicture"]');
                }
                if (!profileInput) {
                    // Fallback: find by type
                    profileInput = document.querySelector('input[type="file"]');
                }

                const profilePreview = document.getElementById('profilePreview');

                if (profileInput && profilePreview) {
                    console.log('Profile picture preview initialized successfully');

                    // Make preview clickable to trigger file input
                    profilePreview.addEventListener('click', function() {
                        profileInput.click();
                    });

                    profileInput.addEventListener('change', function(e) {
                        const file = e.target.files[0];

                        if (file) {
                            console.log('File selected:', file.name, file.type, file.size);

                            // Validate file type
                            if (!file.type.startsWith('image/')) {
                                showAlert('Please select a valid image file (JPG, PNG, GIF, etc.).', 'error');
                                resetPreview();
                                return;
                            }

                            // Validate file size (5MB max)
                            if (file.size > 5 * 1024 * 1024) {
                                showAlert('File size must be less than 5MB. Please choose a smaller image.', 'error');
                                resetPreview();
                                return;
                            }

                            // Show loading state
                            profilePreview.innerHTML = '<i class="fas fa-spinner fa-spin placeholder-icon"></i>';

                            const reader = new FileReader();
                            reader.onload = function(e) {
                                try {
                                    profilePreview.innerHTML = '<img src="' + e.target.result + '" alt="Profile Preview" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                                    console.log('Image preview loaded successfully');
                                } catch (error) {
                                    console.error('Error loading image preview:', error);
                                    showAlert('Error loading image preview. Please try again.', 'error');
                                    resetPreview();
                                }
                            };

                            reader.onerror = function() {
                                console.error('FileReader error');
                                showAlert('Error reading the selected file. Please try again.', 'error');
                                resetPreview();
                            };

                            reader.readAsDataURL(file);
                        } else {
                            resetPreview();
                        }
                    });

                    // Reset preview function
                    function resetPreview() {
                        profilePreview.innerHTML = '<i class="fas fa-user placeholder-icon"></i>';
                        profileInput.value = '';
                    }

                    // Show alert function
                    function showAlert(message, type) {
                        // Create alert element
                        const alertDiv = document.createElement('div');
                        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show`;
                        alertDiv.style.position = 'fixed';
                        alertDiv.style.top = '20px';
                        alertDiv.style.right = '20px';
                        alertDiv.style.zIndex = '9999';
                        alertDiv.style.maxWidth = '400px';
                        alertDiv.innerHTML = `
                            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
                            ${message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;

                        document.body.appendChild(alertDiv);

                        // Auto-remove after 5 seconds
                        setTimeout(() => {
                            if (alertDiv.parentNode) {
                                alertDiv.remove();
                            }
                        }, 5000);
                    }
                } else {
                    console.error('Profile picture elements not found:', {
                        profileInput: !!profileInput,
                        profilePreview: !!profilePreview
                    });
                }
            }

            // Initialize profile picture preview
            initializeProfilePicturePreview();
        });
    </script>
</body>
</html>